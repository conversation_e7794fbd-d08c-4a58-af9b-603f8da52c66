# 人才详情弹窗权限控制实现说明

## 实现概述

已成功为人才详情弹窗添加了路由按钮级别的权限控制，包括以下权限标识：

- `personList`: 人才查询页面权限
- `personDetailDialog`: 人才详情弹窗权限
- `personTag`: 贴标签权限
- `memberInfo`: 会员信息权限

## 具体实现

### 1. 路由级别权限控制

**文件**: `src/router/module/person.ts`

为人才查询页面添加了权限标识：

```typescript
meta: {
  title: '人才查询',
  auth: ['personList']  // 添加页面权限标识
}
```

### 2. 按钮级别权限控制

**文件**: `src/views/person/list/index.vue`

#### 2.1 导入权限检查函数
```typescript
import { auth } from '/@/utils/authFunction'
```

#### 2.2 "简历"按钮权限控制
使用 `v-auth` 指令控制按钮显示：

```html
<el-button 
  type="primary" 
  link 
  size="small" 
  @click="toDetail(scope.row)"
  v-auth="'personDetailDialog'"
>
  简历
</el-button>
```

#### 2.3 JavaScript权限检查
在 `toDetail` 方法中添加权限验证：

```typescript
const toDetail = (row) => {
  if (!row.memberId) {
    ElMessage.error('非法账号')
    return
  }

  // 权限检查：验证用户是否具有人才详情弹窗权限
  if (!auth('personDetailDialog')) {
    ElMessage.error('您没有查看人才详情的权限')
    return
  }

  // ... 其他逻辑
}
```

### 3. 弹窗组件权限控制

**文件**: `src/views/person/components/personDetailDialog.vue`

#### 3.1 导入权限检查函数
```typescript
import { auth } from '/@/utils/authFunction' // 权限检查函数
```

#### 3.2 权限检查函数实现
```typescript
const hasPermission = (permission: string) => {
  return auth(permission)
}
```

#### 3.3 弹窗打开权限检查
在 `open` 方法中添加权限验证：

```typescript
const open = async (memberId, personList, currentIndex, filterConditions) => {
  // 权限检查：验证用户是否具有人才详情弹窗权限
  if (!hasPermission('personDetailDialog')) {
    ElMessage.error('您没有查看人才详情的权限')
    return
  }
  
  // ... 其他逻辑
}
```

#### 3.4 操作按钮权限控制
为弹窗内的操作按钮添加权限控制：

```html
<!-- 贴标签按钮 -->
<el-button 
  type="warning" 
  size="small" 
  @click="handleTag"
  v-auth="'personTag'"
> 
  贴标签 
</el-button>

<!-- 会员信息按钮 -->
<el-button 
  type="primary" 
  size="small" 
  @click="handleViewVipInfo"
  v-auth="'memberInfo'"
>
  会员信息
</el-button>
```

## 权限控制机制说明

### 1. 权限数据存储
权限数据存储在 Vuex 中：`store.state.userInfos.userInfos.authBtnList`

### 2. 权限检查方式

#### 2.1 指令方式（模板中使用）
- `v-auth="'权限标识'"`: 单个权限验证
- `v-auths="['权限1', '权限2']"`: 多个权限验证，满足一个则显示
- `v-auth-all="['权限1', '权限2']"`: 多个权限验证，全部满足则显示

#### 2.2 函数方式（JavaScript中使用）
- `auth('权限标识')`: 单个权限验证
- `auths(['权限1', '权限2'])`: 多个权限验证，满足一个则返回true
- `authAll(['权限1', '权限2'])`: 多个权限验证，全部满足则返回true

### 3. 权限控制层级
1. **路由级别**: 控制页面访问权限
2. **按钮级别**: 控制具体操作权限
3. **功能级别**: 控制弹窗和组件权限

## 测试建议

1. **权限配置测试**：
   - 配置用户具有 `personList` 但没有 `personDetailDialog` 权限
   - 验证用户可以访问人才查询页面，但无法查看详情弹窗

2. **按钮权限测试**：
   - 配置用户没有 `personDetailDialog` 权限
   - 验证"简历"按钮不显示

3. **功能权限测试**：
   - 配置用户没有 `personTag` 权限
   - 验证弹窗中"贴标签"按钮不显示

## 权限标识说明

| 权限标识 | 说明 | 控制范围 |
|---------|------|----------|
| `personList` | 人才查询页面权限 | 控制人才查询页面的访问 |
| `personDetailDialog` | 人才详情弹窗权限 | 控制人才详情弹窗的打开和"简历"按钮显示 |
| `personTag` | 贴标签权限 | 控制弹窗中"贴标签"按钮的显示 |
| `memberInfo` | 会员信息权限 | 控制弹窗中"会员信息"按钮的显示 |

## 注意事项

1. **双重保护**：既在模板中使用指令控制显示，又在方法中进行权限检查，确保安全性
2. **用户体验**：权限不足时显示友好的错误提示信息
3. **扩展性**：权限控制机制可以轻松扩展到其他功能模块
4. **一致性**：遵循项目现有的权限控制模式和命名规范
