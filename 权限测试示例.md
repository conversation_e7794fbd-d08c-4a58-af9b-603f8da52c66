# 人才详情弹窗权限控制测试示例

## 权限配置示例

在登录成功后，可以通过修改用户的权限列表来测试不同的权限场景。

### 1. 完整权限配置（所有功能可用）

```javascript
// 在 src/views/login/index.vue 的 loginSuccess 事件中
const userInfos = {
  account: rs.username || rs.name,
  photo: rs.photo,
  status: rs.status,
  email: rs.email,
  mobile: rs.mobile,
  time: new Date().getTime(),
  authPageList: ['admin', 'personList'], // 页面权限
  authBtnList: [
    'btn.add', 
    'btn.del', 
    'btn.edit', 
    'btn.link',
    'personList',           // 人才查询页面权限
    'personDetailDialog',   // 人才详情弹窗权限
    'personTag',           // 贴标签权限
    'memberInfo'           // 会员信息权限
  ]
}
```

### 2. 受限权限配置（无法查看人才详情）

```javascript
const userInfos = {
  account: rs.username || rs.name,
  photo: rs.photo,
  status: rs.status,
  email: rs.email,
  mobile: rs.mobile,
  time: new Date().getTime(),
  authPageList: ['admin', 'personList'], // 可以访问人才查询页面
  authBtnList: [
    'btn.add', 
    'btn.del', 
    'btn.edit', 
    'btn.link',
    'personList'           // 只有人才查询页面权限，没有详情弹窗权限
    // 注意：没有 'personDetailDialog' 权限
  ]
}
```

### 3. 部分功能权限配置（可查看详情但无法操作）

```javascript
const userInfos = {
  account: rs.username || rs.name,
  photo: rs.photo,
  status: rs.status,
  email: rs.email,
  mobile: rs.mobile,
  time: new Date().getTime(),
  authPageList: ['admin', 'personList'],
  authBtnList: [
    'btn.add', 
    'btn.del', 
    'btn.edit', 
    'btn.link',
    'personList',           // 人才查询页面权限
    'personDetailDialog'    // 人才详情弹窗权限
    // 注意：没有 'personTag' 和 'memberInfo' 权限
  ]
}
```

## 测试场景

### 场景1：完整权限测试
**配置**：包含所有权限
**预期结果**：
- ✅ 可以访问人才查询页面
- ✅ "简历"按钮显示
- ✅ 点击"简历"按钮可以打开人才详情弹窗
- ✅ 弹窗中"贴标签"按钮显示
- ✅ 弹窗中"会员信息"按钮显示

### 场景2：无详情弹窗权限测试
**配置**：移除 `personDetailDialog` 权限
**预期结果**：
- ✅ 可以访问人才查询页面
- ❌ "简历"按钮不显示（被 v-auth 指令隐藏）
- ❌ 即使通过其他方式调用，也会显示权限错误提示

### 场景3：无操作权限测试
**配置**：有 `personDetailDialog` 但无 `personTag` 和 `memberInfo`
**预期结果**：
- ✅ 可以访问人才查询页面
- ✅ "简历"按钮显示
- ✅ 可以打开人才详情弹窗
- ❌ 弹窗中"贴标签"按钮不显示
- ❌ 弹窗中"会员信息"按钮不显示

### 场景4：无页面访问权限测试
**配置**：移除 `personList` 权限
**预期结果**：
- ❌ 无法访问人才查询页面（路由级别拦截）

## 测试步骤

### 1. 修改权限配置
在 `src/views/login/index.vue` 文件中找到 `loginSuccess` 事件处理函数，修改 `authBtnList` 数组：

```javascript
// 找到这个位置（大约在第130-150行）
proxy.mittBus.on('loginSuccess', async (rs: any) => {
  // ... 其他代码
  
  // 修改这里的权限配置
  const adminAuthBtnList: Array<string> = [
    'btn.add', 
    'btn.del', 
    'btn.edit', 
    'btn.link',
    // 根据测试需要添加或移除以下权限
    'personList',           // 人才查询页面权限
    'personDetailDialog',   // 人才详情弹窗权限
    'personTag',           // 贴标签权限
    'memberInfo'           // 会员信息权限
  ]
  
  // ... 其他代码
})
```

### 2. 重新登录
修改权限配置后，需要重新登录以使新的权限配置生效。

### 3. 验证权限控制
1. 访问人才查询页面：`/person/list`
2. 检查"简历"按钮是否显示
3. 点击"简历"按钮测试弹窗是否能正常打开
4. 检查弹窗中的操作按钮是否按权限显示

## 权限控制原理

### 1. 指令级别控制（v-auth）
```html
<el-button v-auth="'personDetailDialog'">简历</el-button>
```
- 如果用户没有 `personDetailDialog` 权限，按钮元素会被从DOM中移除
- 这是前端显示层面的控制

### 2. 函数级别控制（auth()）
```javascript
if (!auth('personDetailDialog')) {
  ElMessage.error('您没有查看人才详情的权限')
  return
}
```
- 在JavaScript逻辑中进行权限检查
- 即使绕过前端显示控制，也会在逻辑层面被拦截

### 3. 双重保护机制
我们的实现采用了双重保护：
1. **显示层保护**：使用 v-auth 指令控制按钮显示
2. **逻辑层保护**：在方法中使用 auth() 函数进行权限验证

这确保了即使有人通过开发者工具修改前端代码，也无法绕过权限控制。

## 注意事项

1. **权限配置生效**：修改权限配置后需要重新登录
2. **缓存清理**：如果权限不生效，可以清理浏览器缓存或使用无痕模式
3. **开发调试**：可以在浏览器控制台中查看当前用户权限：
   ```javascript
   // 查看当前用户的按钮权限列表
   console.log(window.$store.state.userInfos.userInfos.authBtnList)
   ```

## 扩展建议

1. **后端权限验证**：前端权限控制主要用于用户体验，真正的安全控制应该在后端API层面实现
2. **权限管理界面**：可以开发专门的权限管理界面，让管理员可以可视化地配置用户权限
3. **角色权限**：可以基于角色来管理权限，而不是直接给用户分配权限
4. **权限缓存**：对于复杂的权限计算，可以考虑在前端进行缓存优化
