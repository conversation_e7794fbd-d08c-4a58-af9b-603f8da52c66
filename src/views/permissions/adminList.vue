<template>
  <div class="admin-list">
    <div class="box">
      <!-- 顶部搜索和操作区域 -->
      <div id="top-container" class="pb-15">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="登录账户">
            <el-input
              v-model="searchForm.username"
              clearable
              @keyup.enter.native="search"
              placeholder="请输入登录账户"
            ></el-input>
          </el-form-item>
          <el-form-item label="工号">
            <el-input
              v-model="searchForm.jobNumber"
              clearable
              @keyup.enter.native="search"
              placeholder="请输入工号"
            ></el-input>
          </el-form-item>
          <el-form-item label="企业微信id">
            <el-input
              v-model="searchForm.wxWorkUserid"
              clearable
              @keyup.enter.native="search"
              placeholder="请输入企业微信id"
            ></el-input>
          </el-form-item>
          <el-form-item label="账户姓名">
            <el-input
              v-model="searchForm.name"
              clearable
              @keyup.enter.native="search"
              placeholder="请输入账户姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="searchForm.departmentId"
              placeholder="请选择部门"
              filterable
              clearable
            >
              <el-option
                v-for="item in departmentList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色">
            <el-select
              v-model="searchForm.positionId"
              placeholder="请选择角色"
              clearable
              filterable
            >
              <el-option-group
                v-for="department in positionList"
                :key="department.id"
                :label="department.name"
              >
                <el-option
                  v-for="item in department.list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" filterable clearable>
              <el-option v-for="item in statustList" :key="item.k" :label="item.v" :value="item.k">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区域 -->
        <div class="action-bar mt-15">
          <el-button type="primary" @click="showAddForm" class="mr-15">+ 添加账号</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="list"
        border
        :max-height="maxTableHeight"
        v-loading="tableLoading"
        class="admin-table"
      >
        <el-table-column align="center" prop="name" label="姓名" min-width="100" />
        <el-table-column align="center" prop="statusTxt" label="状态" min-width="80" />
        <el-table-column align="center" prop="department" label="部门" min-width="120" />
        <el-table-column align="center" prop="position" label="角色名称" min-width="120" />
        <el-table-column align="center" prop="username" label="登录账户" min-width="120" />
        <el-table-column align="center" prop="jobNumber" label="工号" min-width="100" />
        <el-table-column align="center" prop="wxWorkUserid" label="企业微信id" min-width="140" />
        <el-table-column align="center" prop="addTime" label="创建时间" min-width="160" />
        <el-table-column align="center" label="操作" fixed="right" width="240">
          <template #default="scope">
            <div class="table-button-group">
              <el-button type="primary" size="small" @click="showAddForm(scope.row)">
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="submitChangeStatus(scope.row)"
                v-if="scope.row.status == 0"
              >
                启用
              </el-button>
              <el-button type="warning" size="small" @click="submitChangeStatus(scope.row)" v-else>
                禁用
              </el-button>
              <el-popover
                :width="400"
                trigger="hover"
                placement="left"
                :content="scope.row.permissions"
              >
                <template #reference>
                  <el-button type="info" size="small">权限</el-button>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>

        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </el-table>

      <!-- 底部分页区域 -->
      <div
        id="bottom-container"
        v-show="pages.total > 0"
        class="pt-15 jc-end"
        style="flex-shrink: 0"
      >
        <Paging :total="pages.total" @change="changePage"></Paging>
      </div>
    </div>

    <el-dialog v-model="addFormVisible" title="添加/修改账号">
      <el-form :model="addForm" :label-width="120">
        <el-form-item label="姓名">
          <el-input v-model="addForm.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="addForm.positionId" placeholder="请选择" clearable filterable>
            <el-option-group
              v-for="department in positionList"
              :key="department.id"
              :label="department.name"
            >
              <el-option
                v-for="item in department.list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="登录账户">
          <el-input v-model="addForm.username" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="登录密码">
          <el-input v-model="addForm.password" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="工号">
          <el-input v-model="addForm.jobNumber" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="企业微信全部人员">
          <el-select placeholder="请选择" v-model="addForm.wxWorkUserid" clearable filterable>
            <el-option v-for="item in wxUserList" :label="item.v" :value="item.k"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业微信userid">
          <el-input v-model="addForm.wxWorkUserid" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submit" :loading="submitLoading">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { getCurrentInstance, onMounted, reactive, toRefs, ref, nextTick } from 'vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import {
  addAdmin,
  getAdminList,
  getAdminListParams,
  getPositionMenuList,
  changeStatus
} from '../../api/permissions'
import { getWxUser } from '../../api/admin'
import adminRouteSetting from './component/adminRouteSetting.vue'

export default {
  name: 'adminList',
  components: {
    adminRouteSetting,
    Paging
  },
  setup() {
    const { proxy } = getCurrentInstance() as any
    const maxTableHeight = ref(450)

    const state = reactive({
      searchForm: {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        jobNumber: '',
        page: 1,
        pageSize: ''
      },
      list: [],
      departmentList: <any>[],
      wxUserList: <any>[],
      statustList: [
        {
          k: '',
          v: '全部'
        },
        {
          k: 1,
          v: '启用'
        },
        {
          k: 0,
          v: '禁用'
        }
      ],
      positionList: [],
      statusList: <any>[],
      tableLoading: false,
      pages: {
        currentPage: 1,
        size: 0,
        total: 0
      },
      addForm: {
        id: '',
        name: '',
        username: '',
        positionId: '',
        password: '',
        wxWorkUserid: '',
        jobNumber: ''
      },
      addFormVisible: false,
      submitLoading: false,
      permissionsVisable: false,
      tableData: [],
      checkBoxList: {
        menu: {},
        route: {},
        action: {}
      },
      positionId: ''
    })

    // 动态计算表格高度
    const getTableHeight = async () => {
      await nextTick(() => {})
      const topHeight = document.getElementById('top-container')?.clientHeight || 0
      const height = Number(
        document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
      )
      const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
      // .box 内边框
      const padding = 40
      maxTableHeight.value = height - topHeight - bottomHeight - padding
    }

    // 搜索
    const search = () => {
      state.tableLoading = true
      getAdminList(state.searchForm).then((res: any) => {
        state.list = res.list
        state.pages.size = res.pages.size
        state.pages.total = res.pages.total
        state.tableLoading = false
        // 重新计算表格高度
        getTableHeight()
      })
    }

    // 重置
    const resetSearch = () => {
      state.searchForm = {
        username: '',
        name: '',
        status: '',
        departmentId: '',
        positionId: '',
        jobNumber: '',
        page: 1,
        pageSize: ''
      }
      search()
    }

    const showAddForm = (row: any) => {
      if (row) {
        state.addForm.id = row.id
        state.addForm.name = row.name
        state.addForm.positionId = row.positionId
        state.addForm.username = row.username
        state.addForm.jobNumber = row.jobNumber
        state.addForm.wxWorkUserid = row.wxWorkUserid
      } else {
        state.addForm = {
          id: '',
          name: '',
          username: '',
          positionId: '',
          password: '',
          wxWorkUserid: '',
          jobNumber: ''
        }
      }
      state.addFormVisible = true
    }

    const submitChangeStatus = (row: any) => {
      // 先提示是否确认操作
      const txt = `确定要${row.status === 1 ? '启用' : '禁用'}该用户吗？`
      ElMessageBox.confirm(txt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        changeStatus(row.id).then(() => {
          search()
        })
      })
    }

    onMounted(() => {
      getAdminListParams().then((res: any) => {
        state.positionList = res.positionList
        state.departmentList = res.departmentList
      })

      search()
      loadWxUser()
      // 初始化表格高度
      getTableHeight()
    })

    const loadWxUser = () => {
      getWxUser().then((res: any) => {
        state.wxUserList = res.map((item: any) => {
          return {
            k: item.userid,
            v: `${item.name}(${item.departmentName})`
          }
        })
      })
    }

    const submit = () => {
      state.submitLoading = true
      addAdmin(state.addForm)
        .then(() => {
          state.submitLoading = false
          search()
          state.addFormVisible = false
        })
        .catch((err) => {
          state.submitLoading = false
        })
    }

    const showPermissions = (row: any) => {
      getPositionMenuList(row.id).then((res: any) => {
        state.permissionsVisable = true
        state.tableData = res
        res.forEach((element: any) => {
          if (element.route.isSet == 1) {
            state.checkBoxList.route[`route_${element.route.id}`] = true
          } else {
            state.checkBoxList.route[`route_${element.route.id}`] = false
          }
          if (element.action) {
            element.action.forEach((action: any) => {
              if (action.isSet == 1) {
                state.checkBoxList.action[`action_${action.id}`] = true
              } else {
                state.checkBoxList.action[`action_${action.id}`] = false
              }
            })
          }
        })
        state.positionId = row.id
      })
    }

    const closePositions = () => {
      state.permissionsVisable = false
    }

    const changePage = (r: any) => {
      state.searchForm.page = r.page
      state.searchForm.pageSize = r.limit
      search()
    }
    return {
      ...toRefs(state),
      maxTableHeight,
      search,
      resetSearch,
      showAddForm,
      showPermissions,
      submit,
      changePage,
      closePositions,
      submitChangeStatus,
      getTableHeight
    }
  }
}
</script>

<style scoped lang="scss">
:deep() {
  @import '/@/theme/tableScrollBar.scss';
}

.admin-list {
  .box {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px 15px;
  }

  .search-form {
    .el-form-item {
      margin-bottom: 18px;
      margin-right: 16px;
    }

    .el-input {
      width: 180px;
    }

    .el-select {
      width: 180px;
    }

    .el-button {
      margin-right: 8px;
    }
  }

  .action-bar {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 10px;
    height: 30px;
    line-height: 30px;
  }

  .admin-table {
    .table-button-group {
      display: flex;
      gap: 6px;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;

      .el-button {
        margin: 0;
        padding: 5px 12px;
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  // 工具类样式
  .pb-15 {
    padding-bottom: 15px;
  }

  .pt-15 {
    padding-top: 15px;
  }

  .mt-15 {
    margin-top: 15px;
  }

  .mr-15 {
    margin-right: 15px;
  }

  .jc-end {
    display: flex;
    justify-content: flex-end;
  }

  .jc-between {
    display: flex;
    justify-content: space-between;
  }

  .ai-center {
    display: flex;
    align-items: center;
  }
}

.red {
  color: red;
}

.el-card {
  border: none;
  padding: 0 30px;
  :deep(.el-card__header) {
    padding: 15px 0;
    border-bottom-color: #f2f2f2;
  }
  :deep(.el-card__body) {
    padding: 10px 0 30px;
  }
  .content {
    .title {
      border-left: 2px solid var(--color-primary);
    }
    .btn {
      width: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .cursor-default {
    :deep(.el-input__inner) {
      cursor: inherit;
    }
  }
}
</style>
