import { RouteRecordRaw } from 'vue-router'

export const personRoutes: Array<RouteRecordRaw> = [
  {
    path: '/person',
    name: 'person',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/person/list',
    meta: {
      title: '人才管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/person.svg'
    },
    children: [
      {
        path: '/person/list',
        name: 'personList',
        component: () => import('/@/views/person/list/index.vue'),
        meta: {
          title: '人才查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: '',
          auth: ['personList']
        }
      },

      {
        path: '/person/jobApplyDetail/:id',
        name: 'personJobApplyDetail',
        component: () => import('/@//views/person/jobApplyDetail/index.vue'),
        meta: {
          title: '人才应聘详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/person/resumeList',
        name: 'personResumeList',
        component: () => import('/@/views/person/resumeList/index.vue'),
        meta: {
          title: '附件简历',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
